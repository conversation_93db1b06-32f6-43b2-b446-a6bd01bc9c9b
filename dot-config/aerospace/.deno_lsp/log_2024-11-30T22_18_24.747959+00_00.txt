Starting Deno language server...
{"type":"mark","name":"lsp.initialize","count":1,"args":{"processId":44270,"rootPath":"/Users/<USER>/dotfiles/dot-config/aerospace","rootUri":"file:///Users/<USER>/dotfiles/dot-config/aerospace","initializationOptions":{"enable":null,"cacheOnSave":true,"disablePaths":[],"enablePaths":null,"path":"/Users/<USER>/.local/share/cargo/bin/deno","env":{},"envFile":null,"cache":null,"certificateStores":null,"codeLens":{"implementations":true,"references":true,"referencesAllFunctions":true,"test":true,"testArgs":["--allow-all","--no-check"]},"config":null,"documentPreloadLimit":1000,"future":true,"importMap":null,"inlayHints":{"parameterNames":{"enabled":"none","suppressWhenArgumentMatchesName":true},"parameterTypes":{"enabled":true},"variableTypes":{"enabled":true,"suppressWhenTypeMatchesName":true},"propertyDeclarationTypes":{"enabled":true},"functionLikeReturnTypes":{"enabled":true},"enumMemberValues":{"enabled":true}},"maxTsServerMemory":3072,"suggest":{"autoImports":true,"completeFunctionCalls":true,"names":true,"paths":true,"imports":{"autoDiscover":true,"hosts":{"https://deno.land":true}}},"testing":{"args":["--allow-all","--no-check"]},"tlsCertificate":null,"unsafelyIgnoreCertificateErrors":null,"unstable":true,"lint":true,"internalDebug":false,"internalInspect":false,"logFile":true,"defaultTaskCommand":"open","javascript":{"referencesCodeLens":{"enabled":true,"showOnAllFunctions":true},"suggest":{"completeFunctionCalls":true,"includeAutomaticOptionalChainCompletions":true,"includeCompletionsForImportStatements":true,"names":true,"paths":true,"autoImports":true,"completeJSDocs":true,"jsdoc":{"generateReturns":true},"enabled":true,"classMemberSnippets":{"enabled":true}},"inlayHints":{"parameterNames":{"enabled":"all","suppressWhenArgumentMatchesName":true},"parameterTypes":{"enabled":true},"variableTypes":{"enabled":true,"suppressWhenTypeMatchesName":true},"propertyDeclarationTypes":{"enabled":true},"functionLikeReturnTypes":{"enabled":true},"enumMemberValues":{"enabled":false}},"validate":{"enable":true},"format":{"enable":true,"insertSpaceAfterCommaDelimiter":true,"insertSpaceAfterConstructor":true,"insertSpaceAfterSemicolonInForStatements":true,"insertSpaceBeforeAndAfterBinaryOperators":true,"insertSpaceAfterKeywordsInControlFlowStatements":true,"insertSpaceAfterFunctionKeywordForAnonymousFunctions":true,"insertSpaceBeforeFunctionParenthesis":false,"insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis":true,"insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets":true,"insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces":true,"insertSpaceAfterOpeningAndBeforeClosingEmptyBraces":true,"insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces":true,"insertSpaceAfterOpeningAndBeforeClosingJsxExpressionBraces":false,"placeOpenBraceOnNewLineForFunctions":false,"placeOpenBraceOnNewLineForControlBlocks":false,"semicolons":"ignore"},"implicitProjectConfig":{"checkJs":false,"experimentalDecorators":false},"suggestionActions":{"enabled":true},"preferences":{"quoteStyle":"auto","importModuleSpecifier":"shortest","importModuleSpecifierEnding":"auto","jsxAttributeCompletionStyle":"auto","autoImportFileExcludePatterns":[],"autoImportSpecifierExcludeRegexes":[],"renameShorthandProperties":true,"useAliasesForRenames":true,"renameMatchingJsxTags":true,"organizeImports":{}},"updateImportsOnFileMove":{"enabled":"prompt"},"autoClosingTags":true,"preferGoToSourceDefinition":false,"experimental":{"updateImportsOnPaste":true}},"typescript":{"tsdk":"","disableAutomaticTypeAcquisition":false,"enablePromptUseWorkspaceTsdk":false,"npm":"","check":{"npmIsInstalled":true},"referencesCodeLens":{"enabled":true,"showOnAllFunctions":true},"implementationsCodeLens":{"enabled":true,"showOnInterfaceMethods":true},"tsserver":{"enableTracing":false,"log":"off","pluginPaths":[],"useSeparateSyntaxServer":true,"useSyntaxServer":"auto","maxTsServerMemory":3072,"experimental":{"enableProjectDiagnostics":false,"useVsCodeWatcher":true},"watchOptions":{},"web":{"projectWideIntellisense":{"enabled":true,"suppressSemanticErrors":true},"typeAcquisition":{"enabled":true}},"nodePath":"","enableRegionDiagnostics":true},"suggest":{"completeFunctionCalls":true,"includeAutomaticOptionalChainCompletions":true,"includeCompletionsForImportStatements":true,"paths":true,"autoImports":true,"completeJSDocs":true,"jsdoc":{"generateReturns":true},"enabled":true,"classMemberSnippets":{"enabled":true},"objectLiteralMethodSnippets":{"enabled":true}},"inlayHints":{"parameterNames":{"enabled":"all","suppressWhenArgumentMatchesName":true},"parameterTypes":{"enabled":true},"variableTypes":{"enabled":true,"suppressWhenTypeMatchesName":true},"propertyDeclarationTypes":{"enabled":true},"functionLikeReturnTypes":{"enabled":true},"enumMemberValues":{"enabled":true}},"reportStyleChecksAsWarnings":true,"validate":{"enable":true},"format":{"enable":true,"insertSpaceAfterCommaDelimiter":true,"insertSpaceAfterConstructor":false,"insertSpaceAfterSemicolonInForStatements":true,"insertSpaceBeforeAndAfterBinaryOperators":true,"insertSpaceAfterKeywordsInControlFlowStatements":true,"insertSpaceAfterFunctionKeywordForAnonymousFunctions":true,"insertSpaceBeforeFunctionParenthesis":false,"insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis":true,"insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets":true,"insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces":true,"insertSpaceAfterOpeningAndBeforeClosingEmptyBraces":true,"insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces":true,"insertSpaceAfterOpeningAndBeforeClosingJsxExpressionBraces":true,"insertSpaceAfterTypeAssertion":false,"placeOpenBraceOnNewLineForFunctions":false,"placeOpenBraceOnNewLineForControlBlocks":false,"semicolons":"ignore","indentSwitchCase":true},"tsc":{"autoDetect":"on"},"locale":"auto","suggestionActions":{"enabled":true},"preferences":{"quoteStyle":"auto","importModuleSpecifier":"shortest","importModuleSpecifierEnding":"auto","jsxAttributeCompletionStyle":"auto","includePackageJsonAutoImports":"auto","autoImportFileExcludePatterns":[],"autoImportSpecifierExcludeRegexes":[],"preferTypeOnlyAutoImports":false,"renameShorthandProperties":true,"useAliasesForRenames":true,"renameMatchingJsxTags":true,"organizeImports":{}},"updateImportsOnFileMove":{"enabled":"prompt"},"autoClosingTags":true,"surveys":{"enabled":true},"workspaceSymbols":{"scope":"allOpenProjects","excludeLibrarySymbols":true},"preferGoToSourceDefinition":false,"experimental":{"updateImportsOnPaste":true}},"enableBuiltinCommands":true},"capabilities":{"workspace":{"applyEdit":true,"workspaceEdit":{"documentChanges":true,"resourceOperations":["create","rename","delete"],"failureHandling":"textOnlyTransactional","normalizesLineEndings":true,"changeAnnotationSupport":{"groupsOnLabel":true}},"didChangeConfiguration":{"dynamicRegistration":true},"didChangeWatchedFiles":{"dynamicRegistration":true,"relativePatternSupport":true},"symbol":{"dynamicRegistration":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]},"tagSupport":{"valueSet":[1]},"resolveSupport":{"properties":["location.range"]}},"executeCommand":{"dynamicRegistration":true},"workspaceFolders":true,"configuration":true,"semanticTokens":{"refreshSupport":true},"codeLens":{"refreshSupport":true},"fileOperations":{"dynamicRegistration":true,"didCreate":true,"willCreate":true,"didRename":true,"willRename":true,"didDelete":true,"willDelete":true},"inlineValue":{"refreshSupport":true},"inlayHint":{"refreshSupport":true}},"textDocument":{"synchronization":{"dynamicRegistration":true,"willSave":true,"willSaveWaitUntil":true,"didSave":true},"completion":{"dynamicRegistration":true,"completionItem":{"snippetSupport":true,"commitCharactersSupport":true,"documentationFormat":["markdown","plaintext"],"deprecatedSupport":true,"preselectSupport":true,"tagSupport":{"valueSet":[1]},"insertReplaceSupport":true,"resolveSupport":{"properties":["documentation","detail","additionalTextEdits"]},"insertTextModeSupport":{"valueSet":[1,2]},"labelDetailsSupport":true},"completionItemKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25]},"contextSupport":true,"insertTextMode":2,"completionList":{"itemDefaults":["commitCharacters","editRange","insertTextFormat","insertTextMode"]}},"hover":{"dynamicRegistration":true,"contentFormat":["markdown","plaintext"]},"signatureHelp":{"dynamicRegistration":true,"signatureInformation":{"documentationFormat":["markdown","plaintext"],"parameterInformation":{"labelOffsetSupport":true},"activeParameterSupport":true},"contextSupport":true},"references":{"dynamicRegistration":true},"documentHighlight":{"dynamicRegistration":true},"documentSymbol":{"dynamicRegistration":true,"symbolKind":{"valueSet":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]},"hierarchicalDocumentSymbolSupport":true,"tagSupport":{"valueSet":[1]}},"formatting":{"dynamicRegistration":true},"rangeFormatting":{"dynamicRegistration":true},"onTypeFormatting":{"dynamicRegistration":true},"declaration":{"dynamicRegistration":true,"linkSupport":true},"definition":{"dynamicRegistration":true,"linkSupport":true},"typeDefinition":{"dynamicRegistration":true,"linkSupport":true},"implementation":{"dynamicRegistration":true,"linkSupport":true},"codeAction":{"dynamicRegistration":true,"codeActionLiteralSupport":{"codeActionKind":{"valueSet":["","quickfix","refactor","refactor.extract","refactor.inline","refactor.rewrite","source","source.organizeImports"]}},"isPreferredSupport":true,"disabledSupport":true,"dataSupport":true,"resolveSupport":{"properties":["edit"]},"honorsChangeAnnotations":false},"codeLens":{"dynamicRegistration":true},"documentLink":{"dynamicRegistration":true,"tooltipSupport":true},"colorProvider":{"dynamicRegistration":true},"rename":{"dynamicRegistration":true,"prepareSupport":true,"prepareSupportDefaultBehavior":1,"honorsChangeAnnotations":true},"publishDiagnostics":{"relatedInformation":true,"tagSupport":{"valueSet":[1,2]},"versionSupport":false,"codeDescriptionSupport":true,"dataSupport":true},"foldingRange":{"dynamicRegistration":true,"rangeLimit":5000,"lineFoldingOnly":true,"foldingRangeKind":{"valueSet":["comment","imports","region"]},"foldingRange":{"collapsedText":false}},"selectionRange":{"dynamicRegistration":true},"linkedEditingRange":{"dynamicRegistration":true},"callHierarchy":{"dynamicRegistration":true},"semanticTokens":{"dynamicRegistration":true,"requests":{"range":true,"full":{"delta":true}},"tokenTypes":["namespace","type","class","enum","interface","struct","typeParameter","parameter","variable","property","enumMember","event","function","method","macro","keyword","modifier","comment","string","number","regexp","operator","decorator"],"tokenModifiers":["declaration","definition","readonly","static","deprecated","abstract","async","modification","documentation","defaultLibrary"],"formats":["relative"],"overlappingTokenSupport":false,"multilineTokenSupport":false,"serverCancelSupport":true,"augmentsSyntaxTokens":true},"typeHierarchy":{"dynamicRegistration":true},"inlineValue":{"dynamicRegistration":true},"inlayHint":{"dynamicRegistration":true,"resolveSupport":{"properties":["tooltip","textEdits","label.tooltip","label.location","label.command"]}},"diagnostic":{"dynamicRegistration":true,"relatedDocumentSupport":false}},"notebookDocument":{"synchronization":{"dynamicRegistration":true}},"window":{"workDoneProgress":true,"showMessage":{"messageActionItem":{"additionalPropertiesSupport":true}},"showDocument":{"support":true}},"general":{"regularExpressions":{"engine":"ECMAScript","version":"ES2020"},"markdown":{"parser":"marked","version":"1.1.0"},"staleRequestSupport":{"cancel":true,"retryOnContentModified":["textDocument/semanticTokens/full","textDocument/semanticTokens/range","textDocument/semanticTokens/full/delta"]},"positionEncodings":["utf-16"]},"experimental":{"testingApi":true}},"trace":"off","workspaceFolders":[{"uri":"file:///Users/<USER>/dotfiles/dot-config/aerospace","name":"aerospace"},{"uri":"file:///Users/<USER>/dotfiles/dot-config/sketchybar","name":"sketchybar"}],"clientInfo":{"name":"Cursor","version":"1.93.1"},"locale":"en-gb"}},
  version: 2.1.2 (release, aarch64-apple-darwin)
  executable: /Users/<USER>/.local/share/cargo/bin/deno
Connected to "Cursor" 1.93.1
"deno.inlayHints.parameterTypes.enabled" is deprecated. Instead use "javascript.inlayHints.parameterTypes.enabled" and "typescript.inlayHints.parameterTypes.enabled".
"deno.inlayHints.variableTypes.enabled" is deprecated. Instead use "javascript.inlayHints.variableTypes.enabled" and "typescript.inlayHints.variableTypes.enabled".
"deno.inlayHints.propertyDeclarationTypes.enabled" is deprecated. Instead use "javascript.inlayHints.propertyDeclarationTypes.enabled" and "typescript.inlayHints.propertyDeclarationTypes.enabled".
"deno.inlayHints.functionLikeReturnTypes.enabled" is deprecated. Instead use "javascript.inlayHints.functionLikeReturnTypes.enabled" and "typescript.inlayHints.functionLikeReturnTypes.enabled".
"deno.inlayHints.enumMemberValues.enabled" is deprecated. Instead use "javascript.inlayHints.enumMemberValues.enabled" and "typescript.inlayHints.enumMemberValues.enabled".
"deno.suggest.completeFunctionCalls" is deprecated. Instead use "javascript.suggest.completeFunctionCalls" and "typescript.suggest.completeFunctionCalls".
{"type":"mark","name":"tsc.request.$getSupportedCodeFixes"},
{"type":"mark","name":"tsc.host.$getSupportedCodeFixes","count":1,"args":"GetSupportedCodeFixes"},
{"type":"measure","name":"tsc.host.$getSupportedCodeFixes","count":1,"duration":1.046},
{"type":"measure","name":"tsc.request.$getSupportedCodeFixes","count":1,"duration":88.784},
{"type":"mark","name":"tsc.request.$getAssets"},
{"type":"mark","name":"tsc.host.$getAssets","count":1,"args":"GetAssets"},
{"type":"measure","name":"tsc.host.$getAssets","count":1,"duration":4.723},
{"type":"measure","name":"tsc.request.$getAssets","count":1,"duration":6.457},
{"type":"measure","name":"lsp.initialize","count":1,"duration":99.374},
"deno.inlayHints.parameterTypes.enabled" is deprecated. Instead use "javascript.inlayHints.parameterTypes.enabled" and "typescript.inlayHints.parameterTypes.enabled".
"deno.inlayHints.variableTypes.enabled" is deprecated. Instead use "javascript.inlayHints.variableTypes.enabled" and "typescript.inlayHints.variableTypes.enabled".
"deno.inlayHints.propertyDeclarationTypes.enabled" is deprecated. Instead use "javascript.inlayHints.propertyDeclarationTypes.enabled" and "typescript.inlayHints.propertyDeclarationTypes.enabled".
"deno.inlayHints.functionLikeReturnTypes.enabled" is deprecated. Instead use "javascript.inlayHints.functionLikeReturnTypes.enabled" and "typescript.inlayHints.functionLikeReturnTypes.enabled".
"deno.inlayHints.enumMemberValues.enabled" is deprecated. Instead use "javascript.inlayHints.enumMemberValues.enabled" and "typescript.inlayHints.enumMemberValues.enabled".
"deno.suggest.completeFunctionCalls" is deprecated. Instead use "javascript.suggest.completeFunctionCalls" and "typescript.suggest.completeFunctionCalls".
"deno.inlayHints.parameterTypes.enabled" is deprecated. Instead use "javascript.inlayHints.parameterTypes.enabled" and "typescript.inlayHints.parameterTypes.enabled".
"deno.inlayHints.variableTypes.enabled" is deprecated. Instead use "javascript.inlayHints.variableTypes.enabled" and "typescript.inlayHints.variableTypes.enabled".
"deno.inlayHints.propertyDeclarationTypes.enabled" is deprecated. Instead use "javascript.inlayHints.propertyDeclarationTypes.enabled" and "typescript.inlayHints.propertyDeclarationTypes.enabled".
"deno.inlayHints.functionLikeReturnTypes.enabled" is deprecated. Instead use "javascript.inlayHints.functionLikeReturnTypes.enabled" and "typescript.inlayHints.functionLikeReturnTypes.enabled".
"deno.inlayHints.enumMemberValues.enabled" is deprecated. Instead use "javascript.inlayHints.enumMemberValues.enabled" and "typescript.inlayHints.enumMemberValues.enabled".
"deno.suggest.completeFunctionCalls" is deprecated. Instead use "javascript.suggest.completeFunctionCalls" and "typescript.suggest.completeFunctionCalls".
"deno.inlayHints.parameterTypes.enabled" is deprecated. Instead use "javascript.inlayHints.parameterTypes.enabled" and "typescript.inlayHints.parameterTypes.enabled".
"deno.inlayHints.variableTypes.enabled" is deprecated. Instead use "javascript.inlayHints.variableTypes.enabled" and "typescript.inlayHints.variableTypes.enabled".
"deno.inlayHints.propertyDeclarationTypes.enabled" is deprecated. Instead use "javascript.inlayHints.propertyDeclarationTypes.enabled" and "typescript.inlayHints.propertyDeclarationTypes.enabled".
"deno.inlayHints.functionLikeReturnTypes.enabled" is deprecated. Instead use "javascript.inlayHints.functionLikeReturnTypes.enabled" and "typescript.inlayHints.functionLikeReturnTypes.enabled".
"deno.inlayHints.enumMemberValues.enabled" is deprecated. Instead use "javascript.inlayHints.enumMemberValues.enabled" and "typescript.inlayHints.enumMemberValues.enabled".
"deno.suggest.completeFunctionCalls" is deprecated. Instead use "javascript.suggest.completeFunctionCalls" and "typescript.suggest.completeFunctionCalls".
{"type":"mark","name":"lsp.update_global_cache"},
Enabling import suggestions for: https://deno.land
{"type":"measure","name":"lsp.update_global_cache","count":1,"duration":7.965},
Refreshing configuration tree...
{"type":"mark","name":"lsp.update_cache"},
{"type":"measure","name":"lsp.update_cache","count":1,"duration":0.002},
Server ready.
{"type":"mark","name":"lsp.did_open","count":1,"args":{"textDocument":{"uri":"file:///Users/<USER>/dotfiles/dot-config/Cursor/User/workspace/aerospace.code-workspace","languageId":"jsonc","version":1,"text":"{\n\t\"folders\": [\n\t\t{\n\t\t\t\"path\": \"../../../aerospace\"\n\t\t},\n\t\t{\n\t\t\t\"path\": \"../../../sketchybar\"\n\t\t}\n\t],\n\t\"settings\": {\n\t\t\"workbench.colorCustomizations\": {\n\t\t\t\"activityBar.activeBackground\": \"#6ac2d8\",\n\t\t\t\"activityBar.background\": \"#6ac2d8\",\n\t\t\t\"activityBar.foreground\": \"#15202b\",\n\t\t\t\"activityBar.inactiveForeground\": \"#15202b99\",\n\t\t\t\"activityBarBadge.background\": \"#ca35ac\",\n\t\t\t\"activityBarBadge.foreground\": \"#e7e7e7\",\n\t\t\t\"commandCenter.border\": \"#15202b99\",\n\t\t\t\"editorGroup.border\": \"#6ac2d8\",\n\t\t\t\"panel.border\": \"#6ac2d8\",\n\t\t\t\"sash.hoverBorder\": \"#6ac2d8\",\n\t\t\t\"sideBar.border\": \"#6ac2d8\",\n\t\t\t\"statusBar.background\": \"#41b1ce\",\n\t\t\t\"statusBar.border\": \"#41b1ce\",\n\t\t\t\"statusBar.debuggingBackground\": \"#ce5e41\",\n\t\t\t\"statusBar.debuggingBorder\": \"#ce5e41\",\n\t\t\t\"statusBar.debuggingForeground\": \"#e7e7e7\",\n\t\t\t\"statusBar.foreground\": \"#15202b\",\n\t\t\t\"statusBarItem.hoverBackground\": \"#2d94af\",\n\t\t\t\"statusBarItem.remoteBackground\": \"#41b1ce\",\n\t\t\t\"statusBarItem.remoteForeground\": \"#15202b\",\n\t\t\t\"tab.activeBorder\": \"#6ac2d8\",\n\t\t\t\"titleBar.activeBackground\": \"#41b1ce\",\n\t\t\t\"titleBar.activeForeground\": \"#15202b\",\n\t\t\t\"titleBar.border\": \"#41b1ce\",\n\t\t\t\"titleBar.inactiveBackground\": \"#41b1ce99\",\n\t\t\t\"titleBar.inactiveForeground\": \"#15202b99\"\n\t\t},\n\t\t\"peacock.color\": \"#41b1ce\"\n\t}\n}\n"}}},
{"type":"measure","name":"lsp.did_open","count":1,"duration":0.086},
{"type":"mark","name":"lsp.did_close","count":1,"args":{"textDocument":{"uri":"file:///Users/<USER>/dotfiles/dot-config/Cursor/User/workspace/aerospace.code-workspace"}}},
{"type":"measure","name":"lsp.did_close","count":1,"duration":0.319},
{"type":"mark","name":"lsp.did_change_watched_files","count":1,"args":{"changes":[{"uri":"file:///Users/<USER>/dotfiles/dot-config/aerospace/.vscode/metago_bookmarks.json","type":2},{"uri":"file:///Users/<USER>/dotfiles/dot-config/aerospace/.vscode/metago_bookmarks.json","type":2}]}},
{"type":"measure","name":"lsp.did_change_watched_files","count":1,"duration":0.029},
{"type":"mark","name":"lsp.did_change_watched_files","count":2,"args":{"changes":[{"uri":"file:///Users/<USER>/dotfiles/dot-config/aerospace/.vscode/metago_bookmarks.json","type":2},{"uri":"file:///Users/<USER>/dotfiles/dot-config/aerospace/.vscode/metago_bookmarks.json","type":2}]}},
{"type":"measure","name":"lsp.did_change_watched_files","count":2,"duration":0.016},
{"type":"mark","name":"lsp.did_change_watched_files","count":3,"args":{"changes":[{"uri":"file:///Users/<USER>/dotfiles/dot-config/aerospace/.vscode/metago_bookmarks.json","type":2},{"uri":"file:///Users/<USER>/dotfiles/dot-config/aerospace/.vscode/metago_bookmarks.json","type":2}]}},
{"type":"measure","name":"lsp.did_change_watched_files","count":3,"duration":0.014},
{"type":"mark","name":"lsp.did_change_watched_files","count":4,"args":{"changes":[{"uri":"file:///Users/<USER>/dotfiles/dot-config/aerospace/.vscode/metago_bookmarks.json","type":2},{"uri":"file:///Users/<USER>/dotfiles/dot-config/aerospace/.vscode/metago_bookmarks.json","type":2}]}},
{"type":"measure","name":"lsp.did_change_watched_files","count":4,"duration":0.019},
