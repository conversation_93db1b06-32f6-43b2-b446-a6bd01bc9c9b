after-startup-command = ['exec-and-forget sketchybar']

# Notify Sketchy<PERSON> about workspace change
exec-on-workspace-change = ['/bin/bash', '-c',
  'sketchybar --trigger aerospace_workspace_change FOCUSED_WORKSPACE=$AEROSPACE_FOCUSED_WORKSPACE',
  'exec-and-forget borders active_color=0xffe1e3e4 inactive_color=0xff494d64 width=5.0'
]

# Start AeroSpace at login
start-at-login = false

# Normalizations. See: https://nikitabobko.github.io/AeroSpace/guide#normalization
enable-normalization-flatten-containers = true
enable-normalization-opposite-orientation-for-nested-containers = true

# See: https://nikitabobko.github.io/AeroSpace/guide#layouts
# The 'accordion-padding' specifies the size of accordion padding
# You can set 0 to disable the padding feature
accordion-padding = 30

# Possible values: tiles|accordion
default-root-container-layout = 'tiles'

# Possible values: horizontal|vertical|auto
# 'auto' means: wide monitor (anything wider than high) gets horizontal orientation,
#               tall monitor (anything higher than wide) gets vertical orientation
default-root-container-orientation = 'auto'

# Mouse follows focus when focused monitor changes
# Drop it from your config, if you don't like this behavior
# See https://nikitabobko.github.io/AeroSpace/guide#on-focus-changed-callbacks
# See https://nikitabobko.github.io/AeroSpace/commands#move-mouse
# Fallback value (if you omit the key): on-focused-monitor-changed = []
on-focused-monitor-changed = ['move-mouse monitor-lazy-center']

# You can effectively turn off macOS "Hide application" (cmd-h) feature by toggling this flag
# Useful if you don't use this macOS feature, but accidentally hit cmd-h or cmd-alt-h key
# Also see: https://nikitabobko.github.io/AeroSpace/goodness#disable-hide-app
automatically-unhide-macos-hidden-apps = true

# [[on-window-detected]]
# if.app-id = 'com.apple.systempreferences'
# if.app-name-regex-substring = 'settings'
# if.window-title-regex-substring = 'substring'
# if.workspace = 'workspace-name'
# if.during-aerospace-startup = true
# check-further-callbacks = true
# run = ['layout floating', 'move-node-to-workspace S']  # The callback itself

[[on-window-detected]]
if.app-name-regex-substring = 'telegram'
run = 'layout floating'

[[on-window-detected]]
if.app-name-regex-substring = 'finder'
run = 'layout floating'

# Possible values: (qwerty|dvorak)
# See https://nikitabobko.github.io/AeroSpace/guide#key-mapping
[key-mapping]
preset = 'qwerty'

# Gaps between windows (inner-*) and between monitor edges (outer-*).
# Possible values:
# - Constant:     gaps.outer.top = 8
# - Per monitor:  gaps.outer.top = [{ monitor.main = 16 }, { monitor."some-pattern" = 32 }, 24]
#                 In this example, 24 is a default value when there is no match.
#                 Monitor pattern is the same as for 'workspace-to-monitor-force-assignment'.
#                 See: https://nikitabobko.github.io/AeroSpace/guide#assign-workspaces-to-monitors
[gaps]
inner.horizontal = 10
inner.vertical =   10
outer.bottom =     10
outer.left =       10
outer.right =      10
outer.top =        10

# 'main' binding mode declaration
# See: https://nikitabobko.github.io/AeroSpace/guide#binding-modes
# 'main' binding mode must be always presented
# Fallback value (if you omit the key): mode.main.binding = {}
[mode.main.binding]

# All possible keys:
# - Letters.        a, b, c, ..., z
# - Numbers.        0, 1, 2, ..., 9
# - Keypad numbers. keypad0, keypad1, keypad2, ..., keypad9
# - F-keys.         f1, f2, ..., f20
# - Special keys.   minus, equal, period, comma, slash, backslash, quote, semicolon, backtick,
#                   leftSquareBracket, rightSquareBracket, space, enter, esc, backspace, tab
# - Keypad special. keypadClear, keypadDecimalMark, keypadDivide, keypadEnter, keypadEqual,
#                   keypadMinus, keypadMultiply, keypadPlus
# - Arrows.         left, down, up, right

# All possible modifiers: cmd, alt, ctrl, shift

# All possible commands: https://nikitabobko.github.io/AeroSpace/commands

# See: https://nikitabobko.github.io/AeroSpace/commands#exec-and-forget
# You can uncomment the following lines to open up terminal with alt + enter shortcut (like in i3)
# alt-enter = '''exec-and-forget osascript -e '
alt-enter = '''exec-and-forget osascript -e '
tell application "Wezterm"
    do script
    activate
end tell'
'''

# See: https://nikitabobko.github.io/AeroSpace/commands#focus
alt-h = 'focus left'
alt-j = 'focus down'
alt-k = 'focus up'
alt-l = 'focus right'

# See: https://nikitabobko.github.io/AeroSpace/commands#move
alt-shift-h = 'move left'
alt-shift-j = 'move down'
alt-shift-k = 'move up'
alt-shift-l = 'move right'

alt-ctrl-shift-f = 'fullscreen'
alt-ctrl-f = 'layout floating'

alt-ctrl-h = 'join-with left'
alt-ctrl-j = 'join-with down'
alt-ctrl-k = 'join-with up'
alt-ctrl-l = 'join-with right'

# See: https://nikitabobko.github.io/AeroSpace/commands#layout
alt-slash = 'layout tiles horizontal vertical'
alt-comma = 'layout accordion horizontal vertical'

# See: https://nikitabobko.github.io/AeroSpace/commands#resize
alt-shift-minus = 'resize smart -50'
alt-shift-equal = 'resize smart +50'

# See: https://nikitabobko.github.io/AeroSpace/commands#workspace
alt-ctrl-1 = 'workspace 01'
alt-ctrl-2 = 'workspace 02'
alt-ctrl-3 = 'workspace 03'
alt-ctrl-4 = 'workspace 04'
alt-ctrl-5 = 'workspace 05'
alt-ctrl-6 = 'workspace 06'
alt-ctrl-7 = 'workspace 07'
alt-ctrl-8 = 'workspace 08'
alt-ctrl-9 = 'workspace 09'
alt-ctrl-0 = 'workspace 10'
alt-ctrl-q = 'workspace q'
alt-ctrl-w = 'workspace w'
alt-ctrl-e = 'workspace e'
alt-ctrl-r = 'workspace r'
alt-ctrl-t = 'workspace t'
alt-ctrl-y = 'workspace y'

# See: https://nikitabobko.github.io/AeroSpace/commands#move-node-to-workspace
alt-ctrl-shift-1 = 'move-node-to-workspace 01 --focus-follows-window'
alt-ctrl-shift-2 = 'move-node-to-workspace 02 --focus-follows-window'
alt-ctrl-shift-3 = 'move-node-to-workspace 03 --focus-follows-window'
alt-ctrl-shift-4 = 'move-node-to-workspace 04 --focus-follows-window'
alt-ctrl-shift-5 = 'move-node-to-workspace 05 --focus-follows-window'
alt-ctrl-shift-6 = 'move-node-to-workspace 06 --focus-follows-window'
alt-ctrl-shift-7 = 'move-node-to-workspace 07 --focus-follows-window'
alt-ctrl-shift-8 = 'move-node-to-workspace 08 --focus-follows-window'
alt-ctrl-shift-9 = 'move-node-to-workspace 09 --focus-follows-window'
alt-ctrl-shift-0 = 'move-node-to-workspace 10 --focus-follows-window'
alt-ctrl-shift-q = 'move-node-to-workspace q  --focus-follows-window'
alt-ctrl-shift-w = 'move-node-to-workspace w  --focus-follows-window'
alt-ctrl-shift-e = 'move-node-to-workspace e  --focus-follows-window'
alt-ctrl-shift-r = 'move-node-to-workspace r  --focus-follows-window'
alt-ctrl-shift-t = 'move-node-to-workspace t  --focus-follows-window'
alt-ctrl-shift-y = 'move-node-to-workspace y  --focus-follows-window'

alt-ctrl-a = 'exec-and-forget open -a /Applications/Arc.app'
alt-ctrl-c = 'exec-and-forget open -a /Applications/Cursor.app'
alt-ctrl-d = 'exec-and-forget open -a /Applications/Discord.app'
alt-ctrl-g = 'exec-and-forget open -a /Applications/Ghostty.app'
alt-ctrl-m = 'exec-and-forget open -a /Applications/Telegram.app'
alt-ctrl-n = 'exec-and-forget open -a /Applications/Notion.app'
alt-ctrl-o = 'exec-and-forget open -a /Applications/Obsidian.app'
alt-ctrl-p = 'exec-and-forget open -a /System/Applications/QuickTime Player.app'
alt-ctrl-s = 'exec-and-forget open -a /Applications/Safari.app'
alt-ctrl-x = 'exec-and-forget open -a /Applications/Wavebox.app'
alt-ctrl-z = 'exec-and-forget open -a /Applications/Zed.app'


# See: https://nikitabobko.github.io/AeroSpace/commands#workspace-back-and-forth
alt-tab = 'workspace-back-and-forth'
# See: https://nikitabobko.github.io/AeroSpace/commands#move-workspace-to-monitor
alt-shift-tab = 'move-workspace-to-monitor --wrap-around next'

# See: https://nikitabobko.github.io/AeroSpace/commands#mode
alt-shift-semicolon = 'mode service'
alt-shift-enter = 'mode apps'

# 'service' binding mode declaration.
# See: https://nikitabobko.github.io/AeroSpace/guide#binding-modes
[mode.service.binding]
esc = ['reload-config', 'mode main']
r = ['flatten-workspace-tree', 'mode main'] # reset layout
f = ['layout floating tiling', 'mode main'] # Toggle between floating and tiling layout
backspace = ['close-all-windows-but-current', 'mode main']

# sticky is not yet supported https://github.com/nikitabobko/AeroSpace/issues/2
#s = ['layout sticky tiling', 'mode main']

alt-ctrl-h = ['join-with left', 'mode main']
alt-ctrl-j = ['join-with down', 'mode main']
alt-ctrl-k = ['join-with up', 'mode main']
alt-ctrl-l = ['join-with right', 'mode main']

down = ['volume down', 'mode main']
up = ['volume up', 'mode main']
shift-down = ['volume set 0', 'mode main']
