name: CI

on:
  - push
  - pull_request

jobs:
  spellcheck:
    runs-on: ubuntu-latest

    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: Install codespell
        shell: bash
        run: |
          sudo apt-get update || true
          sudo apt-get install -y codespell

      - name: Run Spellchecker
        run: codespell

  no-features-test:
    name: No-features-test
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: uname -a
        run: uname -a

      - name: configure project
        run: cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DCMAKE_INSTALL_PREFIX=/usr . -DENABLE_VULKAN=OFF -DENABLE_WAYLAND=OFF -DENABLE_XCB_RANDR=OFF -DENABLE_XCB=OFF -DENABLE_XRANDR=OFF -DENABLE_X11=OFF -DENABLE_DRM=OFF -DENABLE_DRM_AMDGPU=OFF -DENABLE_GIO=OFF -DENABLE_DCONF=OFF -DENABLE_DBUS=OFF -DENABLE_XFCONF=OFF -DENABLE_SQLITE3=OFF -DENABLE_RPM=OFF -DENABLE_IMAGEMAGICK7=OFF -DENABLE_IMAGEMAGICK6=OFF -DENABLE_CHAFA=OFF -DENABLE_ZLIB=OFF -DENABLE_EGL=OFF -DENABLE_GLX=OFF -DENABLE_OSMESA=OFF -DENABLE_OPENCL=OFF -DENABLE_FREETYPE=OFF -DENABLE_PULSE=OFF -DENABLE_DDCUTIL=OFF -DENABLE_ELF=OFF -DENABLE_DIRECTX_HEADERS=OFF -DENABLE_THREADS=OFF

      - name: build project
        run: cmake --build . --target package --verbose -j4

      - name: list features
        run: ./fastfetch --list-features

      - name: run fastfetch
        run: time ./fastfetch -c presets/ci.jsonc --stat false

      - name: run fastfetch --format json
        run: time ./fastfetch -c presets/ci.jsonc --format json

      - name: run flashfetch
        run: time ./flashfetch

      - name: print dependencies
        run: ldd fastfetch

      - name: run tests
        run: ctest

  linux-amd64:
    name: Linux-amd64
    runs-on: ubuntu-20.04
    permissions:
      security-events: write
      contents: read
    outputs:
      ffversion: ${{ steps.ffversion.outputs.ffversion }}
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: uname -a
        run: uname -a

      - name: install required packages
        run: sudo apt-get update && sudo apt-get install -y libvulkan-dev libwayland-dev libxrandr-dev libxcb-randr0-dev libdconf-dev libdbus-1-dev libmagickcore-dev libxfconf-0-dev libsqlite3-dev librpm-dev libegl-dev libglx-dev libosmesa6-dev ocl-icd-opencl-dev libpulse-dev libdrm-dev libelf-dev directx-headers-dev python3-requests

      - name: install linuxbrew packages
        run: |
          /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
          /home/<USER>/.linuxbrew/bin/brew install imagemagick chafa ddcutil --ignore-dependencies

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: c

      - name: configure project
        run: PKG_CONFIG_PATH=/home/<USER>/.linuxbrew/lib/pkgconfig:$PKG_CONFIG_PATH cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DENABLE_EMBEDDED_PCIIDS=On -DCMAKE_INSTALL_PREFIX=/usr .

      - name: build project
        run: cmake --build . --target package --verbose -j4

      - name: perform CodeQL analysis
        uses: github/codeql-action/analyze@v3

      - name: list features
        run: ./fastfetch --list-features

      - name: run fastfetch
        run: time ./fastfetch -c presets/ci.jsonc --stat false

      - name: run fastfetch --format json
        run: time ./fastfetch -c presets/ci.jsonc --format json

      - name: run flashfetch
        run: time ./flashfetch

      - name: print dependencies
        run: ldd fastfetch

      - name: run tests
        run: ctest

      - name: get fastfetch version
        id: ffversion
        run: echo "ffversion=$(./fastfetch --version-raw)" >> $GITHUB_OUTPUT

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-linux-amd64
          path: ./fastfetch-*.*

  linux-aarch64:
    name: Linux-aarch64
    runs-on: ubuntu-22.04
    permissions:
      security-events: write
      contents: read
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: run VM
        uses: uraimo/run-on-arch-action@v2
        id: runcmd
        with:
          arch: aarch64
          distro: ubuntu20.04
          githubToken: ${{ github.token }}
          run: |
            uname -a
            apt-get update && apt-get install -y cmake make g++ libvulkan-dev libwayland-dev libxrandr-dev libxcb-randr0-dev libdconf-dev libdbus-1-dev libmagickcore-dev libxfconf-0-dev libsqlite3-dev librpm-dev libegl-dev libglx-dev libosmesa6-dev ocl-icd-opencl-dev libpulse-dev libdrm-dev libelf-dev directx-headers-dev rpm
            cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DCMAKE_INSTALL_PREFIX=/usr .
            cmake --build . --target package --verbose -j4
            ./fastfetch --list-features
            time ./fastfetch -c presets/ci.jsonc --stat false
            time ./fastfetch -c presets/ci.jsonc --format json
            time ./flashfetch
            ldd fastfetch
            ctest

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-linux-aarch64
          path: ./fastfetch-*.*

  linux-armv7:
    name: Linux-armv7
    runs-on: ubuntu-22.04
    permissions:
      security-events: write
      contents: read
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: run VM
        uses: uraimo/run-on-arch-action@v2
        id: runcmd
        with:
          arch: armv7
          distro: ubuntu20.04
          githubToken: ${{ github.token }}
          run: |
            uname -a
            apt-get update && apt-get install -y wget
            # CMake installed by apt has bug `list sub-command REMOVE_ITEM requires two or more arguments`
            wget --no-check-certificate https://apt.kitware.com/ubuntu/pool/main/c/cmake/{cmake_3.29.2-0kitware1ubuntu20.04.1_armhf.deb,cmake-data_3.29.2-0kitware1ubuntu20.04.1_all.deb}
            dpkg -i *.deb
            apt-get install -y make g++ libvulkan-dev libwayland-dev libxrandr-dev libxcb-randr0-dev libdconf-dev libdbus-1-dev libmagickcore-dev libxfconf-0-dev libsqlite3-dev librpm-dev libegl-dev libglx-dev libosmesa6-dev ocl-icd-opencl-dev libpulse-dev libdrm-dev libelf-dev directx-headers-dev rpm
            cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DCMAKE_INSTALL_PREFIX=/usr .
            cmake --build . --target package --verbose -j4
            ./fastfetch --list-features
            time ./fastfetch -c presets/ci.jsonc --stat false
            time ./fastfetch -c presets/ci.jsonc --format json
            time ./flashfetch
            ldd fastfetch
            ctest

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-linux-armv7
          path: ./fastfetch-*.*

  linux-riscv64:
    name: Linux-riscv64
    runs-on: ubuntu-22.04
    permissions:
      security-events: write
      contents: read
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: run VM
        uses: uraimo/run-on-arch-action@v2
        id: runcmd
        with:
          arch: riscv64
          distro: ubuntu22.04
          githubToken: ${{ github.token }}
          run: |
            uname -a
            apt-get update && apt-get install -y cmake make g++ libvulkan-dev libwayland-dev libxrandr-dev libxcb-randr0-dev libdconf-dev libdbus-1-dev libmagickcore-dev libxfconf-0-dev libsqlite3-dev librpm-dev libegl-dev libglx-dev libosmesa6-dev ocl-icd-opencl-dev libpulse-dev libdrm-dev libddcutil-dev libchafa-dev libelf-dev directx-headers-dev rpm
            cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DCMAKE_INSTALL_PREFIX=/usr .
            cmake --build . --target package --verbose -j4
            ./fastfetch --list-features
            time ./fastfetch -c presets/ci.jsonc --stat false
            time ./fastfetch -c presets/ci.jsonc --format json
            time ./flashfetch
            ldd fastfetch
            ctest

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-linux-riscv64
          path: ./fastfetch-*.*

  musl-amd64:
    name: Musl-amd64
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: setup alpine linux
        uses: jirutka/setup-alpine@master
        with:
          branch: v3.19

      - name: install dependencies
        run: |
          cat /etc/alpine-release
          uname -a
          apk add cmake samurai vulkan-loader-dev libxcb-dev wayland-dev libdrm-dev dconf-dev imagemagick-dev chafa-dev zlib-dev dbus-dev mesa-dev opencl-dev xfconf-dev sqlite-dev networkmanager-dev pulseaudio-dev ddcutil-dev elfutils-dev py3-requests gcc g++
        shell: alpine.sh --root {0}

      - name: build
        run: |
            cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DCMAKE_INSTALL_PREFIX=/usr -DIS_MUSL=ON -DENABLE_EMBEDDED_PCIIDS=On -GNinja .
            cmake --build . --target package --verbose -j4
        shell: alpine.sh {0}

      - name: run
        run: |
            ./fastfetch --list-features
            time ./fastfetch -c presets/ci.jsonc --stat false
            time ./fastfetch -c presets/ci.jsonc --format json
            time ./flashfetch
            ldd fastfetch
            ctest
        shell: alpine.sh {0}

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-musl-amd64
          path: ./fastfetch-*.*

  macos-universal:
    name: macOS-universal
    runs-on: macos-12
    permissions:
      security-events: write
      contents: read
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: uname -a
        run: uname -a

      - name: install required packages
        run: |
          HOMEBREW_NO_INSTALLED_DEPENDENTS_CHECK=1 brew install --overwrite vulkan-loader vulkan-headers molten-vk imagemagick chafa

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: c

      - name: configure project
        run: cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DCMAKE_OSX_ARCHITECTURES='arm64;x86_64' .

      - name: build project
        run: cmake --build . --target package --verbose -j4

      - name: perform CodeQL analysis
        uses: github/codeql-action/analyze@v3

      - name: list features
        run: ./fastfetch --list-features

      - name: run fastfetch
        run: time ./fastfetch -c presets/ci.jsonc --stat false

      - name: run fastfetch --format json
        run: time ./fastfetch -c presets/ci.jsonc --format json

      - name: run flashfetch
        run: time ./flashfetch

      - name: print dependencies
        run: otool -L fastfetch

      - name: run tests
        run: ctest

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-macos-universal
          path: ./fastfetch-*.*

  sunos-amd64:
    runs-on: ubuntu-latest
    name: SunOS-amd64
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: run VM
        uses: vmactions/omnios-vm@v1
        with:
          usesh: true
          prepare: |
            uname -a
            pkg update --accept
            pkg install gcc13 cmake git pkg-config glib2 dbus sqlite-3 imagemagick

          run: |
            cmake -DSET_TWEAK=Off -DBUILD_TESTS=On .
            cmake --build . --target package --verbose -j4
            ./fastfetch --list-features
            time ./fastfetch -c presets/ci.jsonc --stat false
            time ./fastfetch -c presets/ci.jsonc --format json
            time ./flashfetch
            ldd fastfetch
            ctest
            cpack

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-sunos-amd64
          path: ./fastfetch-*.*

  freebsd-amd64:
    name: FreeBSD-amd64
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: run VM
        uses: cross-platform-actions/action@master
        with:
          operating_system: freebsd
          architecture: x86-64
          cpu_count: 4
          shell: bash
          version: '14.1'
          run: |
            uname -a
            sudo pkg update
            sudo pkg install -y cmake git pkgconf binutils wayland vulkan-headers vulkan-loader libxcb libXrandr libX11 libdrm glib dconf dbus sqlite3-tcl xfce4-conf egl libosmesa opencl ocl-icd v4l_compat py311-requests chafa
            cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DENABLE_EMBEDDED_PCIIDS=On .
            cmake --build . --target package --verbose -j4
            ./fastfetch --list-features
            time ./fastfetch -c presets/ci.jsonc --stat false
            time ./fastfetch -c presets/ci.jsonc --format json
            time ./flashfetch
            ldd fastfetch
            ctest

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-freebsd-amd64
          path: ./fastfetch-*.*

  dragonfly-amd64:
    name: DragonFly-amd64
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: run VM
        uses: vmactions/dragonflybsd-vm@v1
        with:
          usesh: yes
          prepare: |
            uname -a
            pkg update
            pkg install -y cmake git pkgconf binutils wayland vulkan-headers vulkan-loader libxcb libXrandr libX11 libdrm glib dconf dbus sqlite3-tcl xfce4-conf egl libosmesa opencl ocl-icd v4l_compat py39-requests chafa libelf

          run: |
            cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DENABLE_EMBEDDED_PCIIDS=On .
            cmake --build . --target package --verbose -j4
            ./fastfetch --list-features
            time ./fastfetch -c presets/ci.jsonc --stat false
            time ./fastfetch -c presets/ci.jsonc --format json
            time ./flashfetch
            ldd fastfetch
            ctest

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-dragonfly-amd64
          path: ./fastfetch-*.*

  openbsd-amd64:
    name: OpenBSD-amd64
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: run VM
        uses: cross-platform-actions/action@master
        with:
          operating_system: openbsd
          architecture: x86-64
          cpu_count: 4
          shell: bash
          version: '7.5'
          run: |
            uname -a
            sudo pkg_add -r cmake git pkgconf wayland vulkan-headers vulkan-loader glib2 dconf dbus sqlite3 xfconf imagemagick chafa pulseaudio py3-requests
            cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DENABLE_EMBEDDED_PCIIDS=ON .
            cmake --build . --target package --verbose -j4
            ./fastfetch --list-features
            time ./fastfetch -c presets/ci.jsonc --stat false
            time ./fastfetch -c presets/ci.jsonc --format json
            time ./flashfetch
            ldd fastfetch
            ctest

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-openbsd-amd64
          path: ./fastfetch-*.*

  netbsd-amd64:
    name: NetBSD-amd64
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: run VM
        uses: cross-platform-actions/action@master
        with:
          operating_system: netbsd
          architecture: x86-64
          cpu_count: 4
          shell: bash
          version: '10.0'
          run: |
            uname -a
            sudo pkgin -y install cmake git pkgconf wayland vulkan-headers dconf dbus sqlite3 ImageMagick pulseaudio opencl-headers ocl-icd py312-requests
            cmake -DSET_TWEAK=Off -DBUILD_TESTS=On -DENABLE_EMBEDDED_PCIIDS=ON .
            cmake --build . --target package --verbose -j4
            ./fastfetch --list-features
            time ./fastfetch -c presets/ci.jsonc --stat false
            time ./fastfetch -c presets/ci.jsonc --format json
            time ./flashfetch
            ldd fastfetch
            ctest

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-netbsd-amd64
          path: ./fastfetch-*.*

  windows-amd64:
    name: Windows-amd64
    runs-on: windows-latest
    permissions:
      security-events: write
      contents: read
    defaults:
      run:
        shell: msys2 {0}
    steps:
      - name: checkout repository
        uses: actions/checkout@v4

      - name: setup-msys2
        uses: msys2/setup-msys2@v2
        with:
          msystem: CLANG64
          update: true
          install: git mingw-w64-clang-x86_64-7zip mingw-w64-clang-x86_64-cmake mingw-w64-clang-x86_64-clang mingw-w64-clang-x86_64-vulkan-loader mingw-w64-clang-x86_64-vulkan-headers mingw-w64-clang-x86_64-opencl-icd mingw-w64-clang-x86_64-opencl-headers mingw-w64-clang-x86_64-cppwinrt mingw-w64-clang-x86_64-imagemagick

      - name: print msys version
        run: uname -a

      - name: configure project
        run: env PKG_CONFIG_PATH=/clang64/lib/pkgconfig/:$PKG_CONFIG_PATH cmake -DSET_TWEAK=Off -DBUILD_TESTS=On .

      - name: build project
        run: cmake --build . --verbose -j4

      - name: copy necessary dlls
        run: cp /clang64/bin/{OpenCL,vulkan-1}.dll .

      - name: download amd_ags
        run: curl -LO https://github.com/GPUOpen-LibrariesAndSDKs/AGS_SDK/raw/master/ags_lib/lib/amd_ags_x64.dll

      - name: list features
        run: ./fastfetch --list-features

      - name: run fastfetch
        run: time ./fastfetch -c presets/ci.jsonc --stat false

      - name: run fastfetch --format json
        run: time ./fastfetch -c presets/ci.jsonc --format json

      - name: run flashfetch
        run: time ./flashfetch

      - name: print dependencies
        run: ldd fastfetch

      - name: run tests
        run: ctest

      - name: create zip archive
        run: 7z a -tzip -mx9 -bd -y fastfetch-windows-amd64.zip LICENSE *.dll fastfetch.exe flashfetch.exe presets

      - name: create 7z archive
        run: 7z a -t7z -mx9 -bd -y fastfetch-windows-amd64.7z LICENSE *.dll fastfetch.exe flashfetch.exe presets

      - name: upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: fastfetch-windows-amd64
          path: ./fastfetch-windows-amd64.*

  release:
    if: github.event_name == 'push' && github.ref == 'refs/heads/master' && github.repository == 'fastfetch-cli/fastfetch'
    name: Release
    runs-on: ubuntu-latest
    needs:
      - linux-amd64
      - linux-aarch64
      - linux-armv7
      - linux-riscv64
      - musl-amd64
      - macos-universal
      - freebsd-amd64
      - openbsd-amd64
      - netbsd-amd64
      - dragonfly-amd64
      - sunos-amd64
      - windows-amd64
    permissions:
      contents: write
    steps:
      - name: get latest release version
        id: get_version_release
        uses: pozetroninc/github-action-get-latest-release@master
        with:
          repository: ${{ github.repository }}

      - name: download artifacts
        if: needs.linux-amd64.outputs.ffversion != steps.get_version_release.outputs.release
        uses: actions/download-artifact@v4

      - name: create release
        if: needs.linux-amd64.outputs.ffversion != steps.get_version_release.outputs.release
        uses: ncipollo/release-action@v1
        with:
          tag: ${{ needs.linux-amd64.outputs.ffversion }}
          commit: ${{ github.sha }}
          artifactErrorsFailBuild: true
          artifacts: fastfetch-*/fastfetch-*
          body: "Please refer to [CHANGELOG.md](https://github.com/${{ github.repository }}/blob/${{ needs.linux-amd64.outputs.ffversion }}/CHANGELOG.md) for details."

      - name: download source tarballs
        if: needs.linux-amd64.outputs.ffversion != steps.get_version_release.outputs.release
        run: |
          for i in 1 2 3 4 5; do curl -L --remote-name-all --output-dir fastfetch-source --create-dirs https://github.com/${{ github.repository }}/archive/refs/tags/${{ needs.linux-amd64.outputs.ffversion }}.{tar.gz,zip} && break || sleep 5; done
          ls fastfetch-*/*

      - name: generate release notes
        if: needs.linux-amd64.outputs.ffversion != steps.get_version_release.outputs.release
        run: |
          echo "Please refer to [CHANGELOG.md](https://github.com/${{ github.repository }}/blob/${{ needs.linux-amd64.outputs.ffversion }}/CHANGELOG.md) for details." > fastfetch-release-notes.md
          echo -e "\n---\n\n<details><summary>SHA256SUMs</summary><br>\n\n\`\`\`" >> fastfetch-release-notes.md
          sha256sum fastfetch-*/* >> fastfetch-release-notes.md
          echo -e "\`\`\`\n</details>" >> fastfetch-release-notes.md
          echo -e "\n<details><summary>SHA512SUMs</summary><br>\n\n\`\`\`" >> fastfetch-release-notes.md
          sha512sum fastfetch-*/* >> fastfetch-release-notes.md
          echo -e "\`\`\`\n</details>" >> fastfetch-release-notes.md

      - name: update release body
        if: needs.linux-amd64.outputs.ffversion != steps.get_version_release.outputs.release
        uses: ncipollo/release-action@v1
        with:
          tag: ${{ needs.linux-amd64.outputs.ffversion }}
          commit: ${{ github.sha }}
          bodyFile: fastfetch-release-notes.md
          allowUpdates: true
